name: 💡 Improvement
description: Propose an improvement for existing functionality of the SDK
labels: ["Elixir", "Improvement"]
body:
  - type: markdown
    attributes:
      value: Thanks for taking the time to file a request! Please fill out this form as completely as possible.
  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: A clear and concise description of what you want and what your use case is.
      placeholder: |-
        I want to make whirled peas, but Sentry doesn't blend.
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: Solution Brainstorm
      description: We know you have bright ideas to share ... share away, friend.
      placeholder: |-
        Add a blender to Sentry.
    validations:
      required: true
  - type: markdown
    attributes:
      value: |-
        ## Thanks 🙏
        Check our [triage docs](https://open.sentry.io/triage/) for what to expect next.
