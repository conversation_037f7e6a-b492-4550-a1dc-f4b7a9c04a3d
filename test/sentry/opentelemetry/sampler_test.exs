defmodule Sentry.Opentelemetry.SamplerTest do
  use Sentry.Case, async: true

  alias Sentry.OpenTelemetry.Sampler

  setup do
    # Set a default traces_sample_rate for tests
    put_test_config(traces_sample_rate: 1.0)
    :ok
  end

  describe "span name filtering" do
    test "drops spans with the given name" do
      config = Sampler.setup(%{drop: ["Elixir.Oban.Stager process"]})

      assert {:drop, [], []} =
               Sampler.should_sample(
                 nil,
                 nil,
                 nil,
                 "Elixir.Oban.Stager process",
                 nil,
                 nil,
                 config
               )
    end

    test "records and samples spans not in drop list" do
      config = Sampler.setup(%{drop: ["Elixir.Oban.Stager process"]})

      {decision, _attrs, _tracestate} =
        Sampler.should_sample(nil, nil, nil, "Elixir.Oban.Worker process", nil, nil, config)

      assert decision == :record_and_sample
    end
  end

  describe "sampling based on traces_sample_rate" do
    test "always samples when traces_sample_rate is 1.0" do
      put_test_config(traces_sample_rate: 1.0)
      config = Sampler.setup(%{})

      # Use a deterministic trace ID
      trace_id = 123_456_789

      {decision, _attrs, _tracestate} =
        Sampler.should_sample(nil, trace_id, nil, "test.span", nil, nil, config)

      assert decision == :record_and_sample
    end

    test "never samples when traces_sample_rate is 0.0" do
      put_test_config(traces_sample_rate: 0.0)
      config = Sampler.setup(%{})

      # Use a deterministic trace ID
      trace_id = 123_456_789

      {decision, _attrs, _tracestate} =
        Sampler.should_sample(nil, trace_id, nil, "test.span", nil, nil, config)

      assert decision == :drop
    end

    test "samples deterministically based on trace ID" do
      put_test_config(traces_sample_rate: 0.5)
      config = Sampler.setup(%{})

      # Test with multiple trace IDs to ensure deterministic behavior
      trace_id_1 = 1
      # Close to max value, should not be sampled
      trace_id_2 = 9_223_372_036_854_775_806

      {decision_1, _attrs_1, _tracestate_1} =
        Sampler.should_sample(nil, trace_id_1, nil, "test.span", nil, nil, config)

      {decision_2, _attrs_2, _tracestate_2} =
        Sampler.should_sample(nil, trace_id_2, nil, "test.span", nil, nil, config)

      # Same trace ID should always give same result
      {decision_1_repeat, _attrs_1_repeat, _tracestate_1_repeat} =
        Sampler.should_sample(nil, trace_id_1, nil, "test.span", nil, nil, config)

      assert decision_1 == decision_1_repeat

      # With 50% sampling, we expect different results for different trace IDs
      # trace_id_1 (small value) should be sampled, trace_id_2 (large value) should not
      assert decision_1 == :record_and_sample
      assert decision_2 == :drop
    end
  end

  describe "tracestate handling" do
    test "includes sentry sampling decision in tracestate" do
      put_test_config(traces_sample_rate: 1.0)
      config = Sampler.setup(%{})

      trace_id = 123_456_789

      {decision, _attrs, tracestate} =
        Sampler.should_sample(nil, trace_id, nil, "test.span", nil, nil, config)

      assert decision == :record_and_sample
      assert :otel_tracestate.get("sentry", tracestate) == "1"
    end

    test "includes sentry not-sampled decision in tracestate" do
      put_test_config(traces_sample_rate: 0.0)
      config = Sampler.setup(%{})

      trace_id = 123_456_789

      {decision, _attrs, tracestate} =
        Sampler.should_sample(nil, trace_id, nil, "test.span", nil, nil, config)

      assert decision == :drop
      assert :otel_tracestate.get("sentry", tracestate) == "0"
    end
  end

  describe "description" do
    test "includes sample rate and drop list in description" do
      put_test_config(traces_sample_rate: 0.75)
      config = Sampler.setup(%{drop: ["test.span"]})

      description = Sampler.description(config)

      assert description =~ "SentrySampler"
      assert description =~ "sample_rate=0.75"
      assert description =~ "drop="
    end
  end
end
