if Code.ensure_loaded?(:otel_sampler) do
  defmodule Sentry.OpenTelemetry.Sampler do
    @moduledoc false

    @behaviour :otel_sampler

    require Logger
    require Record
    require OpenTelemetry

    import Bitwise

    # Import OpenTelemetry records
    Record.defrecord(
      :span_ctx,
      Record.extract(:span_ctx, from_lib: "opentelemetry_api/include/opentelemetry.hrl")
    )

    # Sentry-specific tracestate key for storing sampling decisions
    @sentry_tracestate_key "sentry"
    @sentry_sampled_value "1"
    @sentry_not_sampled_value "0"

    def setup(config) do
      # Get traces_sample_rate from Sentry configuration
      traces_sample_rate =
        case Sentry.Config.traces_sample_rate() do
          rate when is_float(rate) and rate >= 0.0 and rate <= 1.0 -> rate
          _ -> 0.0
        end

      config
      |> Map.put(:traces_sample_rate, traces_sample_rate)
      |> Map.put_new(:drop, [])
    end

    def description(config) do
      sample_rate = Map.get(config, :traces_sample_rate, 0.0)
      drop_list = Map.get(config, :drop, [])

      drop_desc = if Enum.empty?(drop_list), do: "", else: " drop=#{inspect(drop_list)}"
      "SentrySampler{sample_rate=#{sample_rate}#{drop_desc}}"
    end

    def should_sample(
          ctx,
          trace_id,
          _links,
          span_name,
          _span_kind,
          _attributes,
          config
        ) do
      # First check if span should be dropped by name
      if span_name in config[:drop] do
        {:drop, [], []}
      else
        # Get current span context to check for parent sampling decision
        span_ctx = :otel_tracer.current_span_ctx(ctx)

        # Make sampling decision based on parent context and sample rate
        case make_sampling_decision(span_ctx, trace_id, config) do
          :sample ->
            tracestate = update_tracestate(span_ctx, @sentry_sampled_value)
            {:record_and_sample, [], tracestate}

          :not_sample ->
            tracestate = update_tracestate(span_ctx, @sentry_not_sampled_value)
            {:drop, [], tracestate}
        end
      end
    end

    # Private functions

    defp make_sampling_decision(span_ctx, trace_id, config) do
      traces_sample_rate = Map.get(config, :traces_sample_rate, 0.0)

      cond do
        # If sampling is disabled, don't sample
        traces_sample_rate == 0.0 ->
          :not_sample

        # If sampling is at 100%, always sample
        traces_sample_rate == 1.0 ->
          :sample

        # Check if parent span has a sampling decision
        has_parent_decision?(span_ctx) ->
          get_parent_sampling_decision(span_ctx)

        # For root spans, make decision based on trace ID and sample rate
        true ->
          make_trace_id_based_decision(trace_id, traces_sample_rate)
      end
    end

    defp has_parent_decision?(span_ctx) do
      case span_ctx do
        span_ctx(tracestate: tracestate) when not is_nil(tracestate) ->
          case :otel_tracestate.get(@sentry_tracestate_key, tracestate) do
            "" -> false
            _value -> true
          end

        _ ->
          false
      end
    end

    defp get_parent_sampling_decision(span_ctx) do
      case span_ctx do
        span_ctx(tracestate: tracestate) when not is_nil(tracestate) ->
          case :otel_tracestate.get(@sentry_tracestate_key, tracestate) do
            @sentry_sampled_value -> :sample
            @sentry_not_sampled_value -> :not_sample
            _ -> :not_sample
          end

        _ ->
          :not_sample
      end
    end

    defp make_trace_id_based_decision(trace_id, sample_rate) do
      # Use the same deterministic sampling logic as the trace_id_ratio_based sampler
      # This ensures consistent sampling decisions for the same trace ID
      # 2^63 - 1
      max_value = 9_223_372_036_854_775_807
      id_upper_bound = trunc(sample_rate * max_value)

      case trace_id do
        nil ->
          :not_sample

        0 ->
          :not_sample

        _ ->
          lower_64_bits = trace_id &&& max_value

          if abs(lower_64_bits) < id_upper_bound do
            :sample
          else
            :not_sample
          end
      end
    end

    defp update_tracestate(span_ctx, sentry_value) do
      case span_ctx do
        span_ctx(tracestate: tracestate) when not is_nil(tracestate) ->
          :otel_tracestate.update(@sentry_tracestate_key, sentry_value, tracestate)

        _ ->
          :otel_tracestate.new([{@sentry_tracestate_key, sentry_value}])
      end
    end
  end
end
